import crypto from 'crypto';

/**
 * Debug script để test cache key generation
 */

function generateLineItemsHash(lineItems) {
  console.log('\n🔍 INPUT line_items:', JSON.stringify(lineItems, null, 2));
  
  // Code hiện tại - chỉ dùng sku
  const sortedItemsCurrent = lineItems
    .map(item => ({
      sku: item.sku,
      quantity: item.quantity,
      price: item.price
    }))
    .sort((a, b) => (a.sku || '').localeCompare(b.sku || ''));
    
  console.log('📝 MAPPED items (current):', JSON.stringify(sortedItemsCurrent, null, 2));
    
  const hashCurrent = crypto.createHash('sha256')
    .update(JSON.stringify(sortedItemsCurrent))
    .digest('hex')
    .substring(0, 12);

  // Code với fallback (theo document)
  const sortedItemsFixed = lineItems
    .map(item => ({
      sku: item.sku || item.name,
      quantity: item.quantity,
      price: item.price
    }))
    .sort((a, b) => a.sku.localeCompare(b.sku));
    
  console.log('📝 MAPPED items (with fallback):', JSON.stringify(sortedItemsFixed, null, 2));
    
  const hashFixed = crypto.createHash('sha256')
    .update(JSON.stringify(sortedItemsFixed))
    .digest('hex')
    .substring(0, 12);

  console.log(`🔑 Hash (current): ${hashCurrent}`);
  console.log(`🔑 Hash (with fallback): ${hashFixed}`);
  
  return { hashCurrent, hashFixed };
}

function generateCacheKey(prefix, domain, ip, userAgent, lineItems) {
  const uaHash = crypto.createHash('sha256')
    .update(userAgent)
    .digest('hex')
    .substring(0, 8);
    
  const { hashCurrent, hashFixed } = generateLineItemsHash(lineItems);
  
  const keyCurrent = `${prefix}:${domain}:${ip}:${uaHash}:${hashCurrent}`;
  const keyFixed = `${prefix}:${domain}:${ip}:${uaHash}:${hashFixed}`;
  
  console.log(`🔑 Cache Key (current): ${keyCurrent}`);
  console.log(`🔑 Cache Key (with fallback): ${keyFixed}`);
  
  return { keyCurrent, keyFixed };
}

// Test cases
console.log('🧪 TEST 1: Products with SKU');
const products1 = [
  { sku: "PROD_001", name: "Product 1", quantity: 1, price: "19.99" },
  { sku: "PROD_002", name: "Product 2", quantity: 2, price: "29.99" }
];

generateCacheKey('draft_order', 'example.com', '127.0.0.1', 'Mozilla/5.0 (Test)', products1);

console.log('\n🧪 TEST 2: Products without SKU (only name)');
const products2 = [
  { name: "Product 1", quantity: 1, price: "19.99" },
  { name: "Product 2", quantity: 2, price: "29.99" }
];

generateCacheKey('draft_order', 'example.com', '127.0.0.1', 'Mozilla/5.0 (Test)', products2);

console.log('\n🧪 TEST 3: Same products, different order');
const products3 = [
  { sku: "PROD_002", name: "Product 2", quantity: 2, price: "29.99" },
  { sku: "PROD_001", name: "Product 1", quantity: 1, price: "19.99" }
];

generateCacheKey('draft_order', 'example.com', '127.0.0.1', 'Mozilla/5.0 (Test)', products3);

console.log('\n🧪 TEST 4: Different products');
const products4 = [
  { sku: "PROD_003", name: "Product 3", quantity: 1, price: "39.99" }
];

generateCacheKey('draft_order', 'example.com', '127.0.0.1', 'Mozilla/5.0 (Test)', products4);

console.log('\n🧪 TEST 5: Same products but different quantities');
const products5 = [
  { sku: "PROD_001", name: "Product 1", quantity: 2, price: "19.99" },
  { sku: "PROD_002", name: "Product 2", quantity: 1, price: "29.99" }
];

generateCacheKey('draft_order', 'example.com', '127.0.0.1', 'Mozilla/5.0 (Test)', products5); 