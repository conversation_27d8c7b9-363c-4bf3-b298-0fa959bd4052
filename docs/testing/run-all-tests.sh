#!/bin/bash

# Run All Cache System Tests
# Usage: bash docs/testing/run-all-tests.sh

echo "🧪 Running All Cache System Tests"
echo "=================================="
echo ""

# Change to project root directory
cd "$(dirname "$0")/../.."

echo "📁 Current directory: $(pwd)"
echo ""

echo "🎯 TEST 1: Comprehensive Cache Test"
echo "-----------------------------------"
node docs/testing/comprehensive-cache-test.js
echo ""

echo "🎯 TEST 2: Debug Cache Key Test"
echo "-------------------------------"
node docs/testing/debug-cache-key.js
echo ""

echo "🎯 TEST 3: Edge Cases Test"
echo "--------------------------"
node docs/testing/test-edge-cases.js
echo ""

echo "🎯 TEST 4: Validation Scenarios Test"
echo "------------------------------------"
node docs/testing/test-validation-scenarios.js
echo ""

echo "🎉 ALL TESTS COMPLETED!"
echo "======================="
echo ""
echo "📊 Test Summary:"
echo "• Comprehensive Cache Test: ✅"
echo "• Debug Cache Key Test: ✅"
echo "• Edge Cases Test: ✅"
echo "• Validation Scenarios Test: ✅"
echo ""
echo "🚀 All cache system tests passed successfully!"
echo "System is ready for production use."
