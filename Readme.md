# TrueStore Order Service

## 🚀 Overview

TrueStore Order Service is a robust Node.js application for managing e-commerce orders with PayPal integration, advanced caching, and comprehensive validation systems.

## 📚 Documentation

**Complete documentation is available in the [docs/](./docs/) directory:**

- **[📖 Documentation Index](./docs/README.md)** - Start here for all documentation
- **[🔗 API Reference](./docs/api/API_Documentation.md)** - Complete API documentation
- **[🧠 Cache System](./docs/cache-system/CACHE_SYSTEM_DOCUMENTATION.md)** - Cache system guide
- **[🧪 Testing](./docs/testing/TEST_RESULTS_SUMMARY.md)** - Test results and validation

## 🎯 Key Features

- ✅ **PayPal Integration** - Complete order creation and capture flow
- ✅ **Advanced Caching** - Multi-layer validation with Redis
- ✅ **Duplicate Prevention** - Zero duplicate orders within 5 minutes
- ✅ **Performance Optimization** - 45ms cached responses vs 800ms
- ✅ **Comprehensive Testing** - 32 test scenarios covering all use cases
- ✅ **Rate Limiting** - Public API protection
- ✅ **Error Handling** - Robust validation and error responses

## 🏃‍♂️ Quick Start

### Prerequisites
- Node.js 16+
- Redis server
- PostgreSQL database
- PayPal developer account

### Installation
```bash
npm install
```

### Environment Setup
```bash
cp .env.example .env
# Configure your environment variables
```

### Database Migration
```bash
npx sequelize-cli db:migrate
```

### Start Services
```bash
# Start main application
pm2 start ecosystem.config.cjs

# Start workers
pm2 start src/workers/emailWorker.js --name "email-worker"
pm2 start ecosystem.config.cjs --only order-check-worker
```

## 🧪 Testing

Run all cache system tests:
```bash
bash docs/testing/run-all-tests.sh
```

Individual test suites:
```bash
node docs/testing/comprehensive-cache-test.js
node docs/testing/test-edge-cases.js
node docs/testing/test-validation-scenarios.js
```

## 📊 Test Results
- **Total Test Cases**: 32
- **Passed**: 32 ✅
- **Failed**: 0 ❌
- **Coverage**: 100%

## 🔧 Development Notes

### Supported Domains
```
orders-002.com, orders-003.com, orders-222.com, orders-666.com, orders-888.com
orders-dth.com, orders-nkh.com, orders-novapeak.com, orders-emberedge.com
orders-swifthive.com, orders-skybloom.com
```

### PayPal Sandbox Configuration
```
URL: https://sandbox.paypal.com
Region: US
Business Email: <EMAIL>
Personal Email: <EMAIL>
```

### Database Setup
```sql
-- Create user for domain access
CREATE USER 'usertruestore_v_4a482'@'%' IDENTIFIED BY 'k23k@hab129BB';
GRANT SELECT ON truestore_v_4a482.wp_danhsachdomain TO 'usertruestore_v_4a482'@'%';
FLUSH PRIVILEGES;

-- Add order_key column
ALTER TABLE truestore.Orders
ADD COLUMN order_key VARCHAR(255) NULL AFTER id;
```

### Useful Scripts
```bash
# Sync sale report by date
node src/scripts/syncSaleReportByDate.js 12-02-2025 mysticraftus.com

# Start specific worker
pm2 start ecosystem.config.cjs --only order-check-worker
```

## 📧 Contact
- **AWS Email**: <EMAIL>

## 🔄 Recent Updates

### **2024-12-18 - Cache System V2**
- ✅ Fixed draft order validation with order status check
- ✅ Added invoice collision prevention
- ✅ Implemented stale cache detection
- ✅ Enhanced error handling with multiple validation layers
- ✅ Comprehensive testing with 32 test scenarios
- ✅ Organized documentation in `docs/` directory

## 📖 Learn More

Visit the [documentation directory](./docs/) for comprehensive guides, API references, and testing information.

---

**🚀 Production-ready order service with robust caching and validation! 🎯**