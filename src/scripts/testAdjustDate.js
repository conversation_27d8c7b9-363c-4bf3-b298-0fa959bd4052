import { adjustDate } from "../utils/utils.js";

/**
 * Test adjustDate function với các cases khác nhau
 */
function testAdjustDate() {
  console.log("=== TESTING adjustDate() FUNCTION ===\n");
  
  const testCases = [
    // Case 1: Order tạo đầu ngày (có thể bị lùi về ngày trước)
    {
      input: "2025-06-21T00:07:43.000Z",
      description: "Order tạo lúc 00:07 UTC (7:07 sáng VN)"
    },
    {
      input: "2025-06-21T01:30:00.000Z", 
      description: "Order tạo lúc 01:30 UTC (8:30 sáng VN)"
    },
    {
      input: "2025-06-21T06:59:59.000Z",
      description: "Order tạo lúc 06:59 UTC (13:59 chiều VN)"
    },
    
    // Case 2: Order tạo sau 7h UTC (sẽ vẫn trong ngày)
    {
      input: "2025-06-21T07:00:00.000Z",
      description: "Order tạo lúc 07:00 UTC (14:00 chiều VN)"
    },
    {
      input: "2025-06-21T12:00:00.000Z",
      description: "Order tạo lúc 12:00 UTC (19:00 tối VN)"
    },
    {
      input: "2025-06-21T23:59:59.000Z",
      description: "Order tạo lúc 23:59 UTC (6:59 sáng hôm sau VN)"
    },
    
    // Case 3: Boundary cases
    {
      input: "2025-06-20T17:00:00.000Z",
      description: "Order tạo lúc 17:00 UTC ngày 20 (0:00 ngày 21 VN)"
    },
    {
      input: "2025-06-21T16:59:59.000Z", 
      description: "Order tạo lúc 16:59 UTC ngày 21 (23:59 ngày 21 VN)"
    }
  ];
  
  testCases.forEach((testCase, index) => {
    const result = adjustDate(testCase.input);
    const originalDate = new Date(testCase.input);
    const adjustedDate = new Date(testCase.input);
    adjustedDate.setHours(adjustedDate.getHours() - 7);
    
    console.log(`Test ${index + 1}:`);
    console.log(`  Input: ${testCase.input}`);
    console.log(`  Description: ${testCase.description}`);
    console.log(`  Original UTC: ${originalDate.toISOString()}`);
    console.log(`  After -7h: ${adjustedDate.toISOString()}`);
    console.log(`  adjustDate() result: ${result}`);
    console.log(`  Expected date (VN timezone): ${originalDate.toLocaleDateString('en-CA', {timeZone: 'Asia/Ho_Chi_Minh'})}`);
    
    // Check if result matches expected VN date
    const vnDate = originalDate.toLocaleDateString('en-CA', {timeZone: 'Asia/Ho_Chi_Minh'});
    const isCorrect = result === vnDate;
    console.log(`  ✅ Correct: ${isCorrect ? 'YES' : 'NO'}`);
    
    if (!isCorrect) {
      console.log(`  ❌ MISMATCH: Expected ${vnDate}, got ${result}`);
    }
    console.log("");
  });
  
  // Test với orders thực tế từ validation result
  console.log("=== TESTING WITH REAL ORDER DATA ===\n");
  
  const realOrderCases = [
    {
      id: 787432,
      createdAt: "2025-06-21T00:07:43.000Z",
      expectedSaleReportDate: "2025-06-21"
    },
    {
      id: 787435,
      createdAt: "2025-06-21T00:18:22.000Z", 
      expectedSaleReportDate: "2025-06-21"
    },
    {
      id: 787438,
      createdAt: "2025-06-21T00:24:32.000Z",
      expectedSaleReportDate: "2025-06-21"
    }
  ];
  
  realOrderCases.forEach(orderCase => {
    const adjustedResult = adjustDate(orderCase.createdAt);
    const isMatch = adjustedResult === orderCase.expectedSaleReportDate;
    
    console.log(`Order ${orderCase.id}:`);
    console.log(`  Created: ${orderCase.createdAt}`);
    console.log(`  adjustDate(): ${adjustedResult}`);
    console.log(`  Expected: ${orderCase.expectedSaleReportDate}`);
    console.log(`  Match: ${isMatch ? '✅ YES' : '❌ NO'}`);
    console.log("");
  });
  
  // Summary
  console.log("=== SUMMARY ===");
  console.log("adjustDate() function:");
  console.log("- Subtracts 7 hours from UTC time");
  console.log("- Converts to YYYY-MM-DD format");
  console.log("- Purpose: Convert UTC to Vietnam timezone (UTC+7)");
  console.log("");
  console.log("Potential issues:");
  console.log("- Orders created 00:00-06:59 UTC will be moved to previous day");
  console.log("- This might cause date mismatches in reports");
  console.log("- But this is intentional for Vietnam timezone alignment");
}

// Run test
if (import.meta.url === `file://${process.argv[1]}`) {
  testAdjustDate();
}
