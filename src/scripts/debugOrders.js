import { Op } from "sequelize";
import Order from "../models/order.js";
import { convertToClientTimezone } from "../utils/utils.js";
import logger from "../utils/logger.js";

const debugOrders = async (date) => {
  try {
    console.log(`\n=== DEBUG ORDERS FOR DATE: ${date} ===`);
    
    // 1. Check total orders in database
    const totalOrders = await Order.count();
    console.log(`Total orders in database: ${totalOrders}`);
    
    // 2. Check orders by status
    const statusCounts = await Order.findAll({
      attributes: [
        'status',
        [Order.sequelize.fn('COUNT', Order.sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });
    console.log('Orders by status:', statusCounts);
    
    // 3. Check recent orders (last 10)
    const recentOrders = await Order.findAll({
      limit: 10,
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'domain', 'status', 'createdAt', 'transaction_id']
    });
    console.log('\nRecent orders:');
    recentOrders.forEach(order => {
      console.log(`- ID: ${order.id}, Domain: ${order.domain}, Status: ${order.status}, Created: ${order.createdAt}, TxnID: ${order.transaction_id}`);
    });
    
    // 4. Check orders for specific date with different approaches
    console.log(`\n=== CHECKING DATE: ${date} ===`);
    
    // Approach 1: Using convertToClientTimezone (like validation script)
    const { startDate, endDate } = convertToClientTimezone(date, date);
    console.log(`Timezone converted range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
    
    const ordersWithTimezone = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      },
      attributes: ['id', 'domain', 'status', 'createdAt', 'transaction_id']
    });
    console.log(`Orders found with timezone conversion: ${ordersWithTimezone.length}`);
    
    // Approach 2: Simple date range (UTC)
    const [day, month, year] = date.split('-');
    const utcStart = new Date(year, month - 1, day, 0, 0, 0);
    const utcEnd = new Date(year, month - 1, day, 23, 59, 59);
    console.log(`UTC range: ${utcStart.toISOString()} to ${utcEnd.toISOString()}`);
    
    const ordersUTC = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [utcStart, utcEnd]
        }
      },
      attributes: ['id', 'domain', 'status', 'createdAt', 'transaction_id']
    });
    console.log(`Orders found with UTC range: ${ordersUTC.length}`);
    
    // Approach 3: Check orders around that date (±1 day)
    const dayBefore = new Date(utcStart);
    dayBefore.setDate(dayBefore.getDate() - 1);
    const dayAfter = new Date(utcEnd);
    dayAfter.setDate(dayAfter.getDate() + 1);
    
    const ordersAround = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [dayBefore, dayAfter]
        }
      },
      attributes: ['id', 'domain', 'status', 'createdAt', 'transaction_id'],
      order: [['createdAt', 'DESC']]
    });
    console.log(`Orders found in ±1 day range: ${ordersAround.length}`);
    if (ordersAround.length > 0) {
      console.log('Orders around the date:');
      ordersAround.forEach(order => {
        console.log(`- ID: ${order.id}, Domain: ${order.domain}, Status: ${order.status}, Created: ${order.createdAt}`);
      });
    }
    
    // 5. Check processing orders specifically
    const processingOrders = await Order.findAll({
      where: {
        status: 'processing'
      },
      limit: 10,
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'domain', 'status', 'createdAt', 'transaction_id']
    });
    console.log(`\nRecent processing orders: ${processingOrders.length}`);
    processingOrders.forEach(order => {
      console.log(`- ID: ${order.id}, Domain: ${order.domain}, Created: ${order.createdAt}`);
    });
    
  } catch (error) {
    console.error('Error debugging orders:', error);
  }
};

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const date = process.argv[2] || '22-06-2025';
  debugOrders(date)
    .then(() => {
      console.log('\nDebug completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Debug failed:', error);
      process.exit(1);
    });
}
