import { Op } from "sequelize";
import Order from "../models/order.js";
import SaleReport from "../models/saleReport.js";
import DomainReport from "../models/domainReport.js";
import { adjustDate, convertToClientTimezone } from "../utils/utils.js";
import currency from "currency.js";
import logger from "../utils/logger.js";

/**
 * Validate and reconcile sale report data with actual orders
 * @param {string} date - Date in format DD-MM-YYYY
 * @param {string} domain - Optional domain filter
 * @param {boolean} fix - Whether to fix discrepancies automatically
 */
export const validateSaleReportData = async (date, domain = null, fix = false) => {
  try {
    logger.info("Starting sale report validation", { date, domain, fix });

    // Use the same timezone conversion as other parts of the system
    const { startDate, endDate } = convertToClientTimezone(date, date);

    const whereClause = {
      status: {
        [Op.in]: ["processing", "shipped", "completed"]  // All paid orders
      },
      createdAt: {
        [Op.between]: [startDate, endDate],
      },
    };

    if (domain) {
      whereClause.domain = domain;
    }

    logger.info("Query parameters", {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      domain: domain || 'all',
      whereClause
    });

    const orders = await Order.findAll({
      where: whereClause,
      order: [['createdAt', 'ASC']],
    });

    logger.info(`Found ${orders.length} paid orders for validation (processing/shipped/completed)`);

    // Group orders by domain and date
    const ordersByDomainDate = {};
    orders.forEach(order => {
      const orderDate = adjustDate(order.createdAt);
      const key = `${order.domain}_${orderDate}`;
      
      if (!ordersByDomainDate[key]) {
        ordersByDomainDate[key] = {
          domain: order.domain,
          date: orderDate,
          orders: [],
          totalMoney: 0,
          totalOrders: 0,
        };
      }
      
      ordersByDomainDate[key].orders.push(order);
      ordersByDomainDate[key].totalMoney = currency(ordersByDomainDate[key].totalMoney)
        .add(order.orderData.total).value;
      ordersByDomainDate[key].totalOrders += 1;
    });

    const discrepancies = [];

    // Validate each domain-date combination
    for (const [, expectedData] of Object.entries(ordersByDomainDate)) {
      const { domain: orderDomain, date: orderDate } = expectedData;

      // Get sale report
      const saleReport = await SaleReport.findOne({
        where: {
          domain: orderDomain,
          date: orderDate,
        },
      });

      // Get domain report
      const domainReport = await DomainReport.findOne({
        where: {
          domain: orderDomain,
          date: orderDate,
        },
      });

      const discrepancy = {
        domain: orderDomain,
        date: orderDate,
        expected: expectedData,
        saleReport: saleReport ? {
          orders: saleReport.orders,
          money: parseFloat(saleReport.money),
          orderIds: saleReport.orderIds,
        } : null,
        domainReport: domainReport ? {
          totalOrders: domainReport.Stats[0]?.total_orders || 0,
          totalSales: domainReport.Stats[0]?.total_sales || 0,
          orderIds: domainReport.orderIds,
        } : null,
        issues: [],
      };

      // Check sale report discrepancies
      if (!saleReport) {
        discrepancy.issues.push("Sale report missing");
      } else {
        if (saleReport.orders !== expectedData.totalOrders) {
          discrepancy.issues.push(`Sale report orders mismatch: expected ${expectedData.totalOrders}, got ${saleReport.orders}`);
        }
        if (Math.abs(parseFloat(saleReport.money) - expectedData.totalMoney) > 0.01) {
          discrepancy.issues.push(`Sale report money mismatch: expected ${expectedData.totalMoney}, got ${saleReport.money}`);
        }
        
        // Check if all order IDs are included
        const expectedOrderIds = expectedData.orders.map(o => o.id);
        const missingOrderIds = expectedOrderIds.filter(id => !saleReport.orderIds.includes(id));
        const extraOrderIds = saleReport.orderIds.filter(id => !expectedOrderIds.includes(id));
        
        if (missingOrderIds.length > 0) {
          discrepancy.issues.push(`Sale report missing order IDs: ${missingOrderIds.join(', ')}`);
        }
        if (extraOrderIds.length > 0) {
          discrepancy.issues.push(`Sale report has extra order IDs: ${extraOrderIds.join(', ')}`);
        }
      }

      // Check domain report discrepancies
      if (!domainReport) {
        discrepancy.issues.push("Domain report missing");
      } else {
        const domainStats = domainReport.Stats[0] || {};
        if (domainStats.total_orders !== expectedData.totalOrders) {
          discrepancy.issues.push(`Domain report orders mismatch: expected ${expectedData.totalOrders}, got ${domainStats.total_orders}`);
        }
        if (Math.abs(domainStats.total_sales - expectedData.totalMoney) > 0.01) {
          discrepancy.issues.push(`Domain report sales mismatch: expected ${expectedData.totalMoney}, got ${domainStats.total_sales}`);
        }
        
        // Check order IDs
        const expectedOrderIds = expectedData.orders.map(o => o.id);
        const missingOrderIds = expectedOrderIds.filter(id => !domainReport.orderIds.includes(id));
        const extraOrderIds = domainReport.orderIds.filter(id => !expectedOrderIds.includes(id));
        
        if (missingOrderIds.length > 0) {
          discrepancy.issues.push(`Domain report missing order IDs: ${missingOrderIds.join(', ')}`);
        }
        if (extraOrderIds.length > 0) {
          discrepancy.issues.push(`Domain report has extra order IDs: ${extraOrderIds.join(', ')}`);
        }
      }

      if (discrepancy.issues.length > 0) {
        discrepancies.push(discrepancy);
        logger.warn("Data discrepancy found", {
          domain: orderDomain,
          date: orderDate,
          issues: discrepancy.issues,
        });
      }
    }

    // Auto-fix if requested
    if (fix && discrepancies.length > 0) {
      logger.info("Starting auto-fix for discrepancies");
      
      for (const discrepancy of discrepancies) {
        const { domain: fixDomain, date: fixDate, expected } = discrepancy;
        
        // Delete existing reports
        await SaleReport.destroy({
          where: { domain: fixDomain, date: fixDate }
        });
        await DomainReport.destroy({
          where: { domain: fixDomain, date: fixDate }
        });
        
        // Recreate reports with correct data
        const { saleReportService } = await import("../services/saleReportService.js");
        const { updateDomainReport } = await import("../services/domainReportService.js");
        
        for (const order of expected.orders) {
          await saleReportService.updateSaleReport(order);
          await updateDomainReport(order);
        }
        
        logger.info("Fixed discrepancy", {
          domain: fixDomain,
          date: fixDate,
          ordersProcessed: expected.orders.length,
        });
      }
    }

    const summary = {
      totalDomainsChecked: Object.keys(ordersByDomainDate).length,
      totalOrdersChecked: orders.length,
      discrepanciesFound: discrepancies.length,
      discrepancies: discrepancies,
    };

    logger.info("Sale report validation completed", summary);
    return summary;

  } catch (error) {
    logger.error("Error validating sale report data", {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const date = process.argv[2];
  const domain = process.argv[3] || null;
  const fix = process.argv[4] === 'true';

  if (!date) {
    console.log("Usage: node validateSaleReportData.js <date> [domain] [fix]");
    console.log("Example: node validateSaleReportData.js 22-06-2025 truestore.us true");
    process.exit(1);
  }

  validateSaleReportData(date, domain, fix)
    .then(result => {
      console.log("Validation completed:", JSON.stringify(result, null, 2));
      process.exit(0);
    })
    .catch(error => {
      console.error("Validation failed:", error);
      process.exit(1);
    });
}
