import { Op } from "sequelize";
import Order from "../models/order.js";
import SaleReport from "../models/saleReport.js";
import DomainReport from "../models/domainReport.js";
import { adjustDate, convertToClientTimezone } from "../utils/utils.js";
import currency from "currency.js";
import logger from "../utils/logger.js";

/**
 * Analyze extra orders to understand why they're in reports
 */
async function analyzeExtraOrders(discrepancy, domain, date) {
  try {
    const extraOrderIds = discrepancy.analysis.extraOrders.orderIds;

    // Get details of extra orders
    const extraOrders = await Order.findAll({
      where: {
        id: {
          [Op.in]: extraOrderIds
        }
      },
      attributes: ['id', 'domain', 'status', 'createdAt', 'transaction_id']
    });

    const analysis = {
      orderDetails: [],
      dateDistribution: {},
      statusDistribution: {},
      domainDistribution: {},
      possibleCauses: []
    };

    extraOrders.forEach(order => {
      const orderDate = adjustDate(order.createdAt);
      const orderInfo = {
        id: order.id,
        domain: order.domain,
        status: order.status,
        createdAt: order.createdAt,
        adjustedDate: orderDate,
        transaction_id: order.transaction_id
      };

      analysis.orderDetails.push(orderInfo);

      // Count by date
      analysis.dateDistribution[orderDate] = (analysis.dateDistribution[orderDate] || 0) + 1;

      // Count by status
      analysis.statusDistribution[order.status] = (analysis.statusDistribution[order.status] || 0) + 1;

      // Count by domain
      analysis.domainDistribution[order.domain] = (analysis.domainDistribution[order.domain] || 0) + 1;
    });

    // Determine possible causes
    const dates = Object.keys(analysis.dateDistribution);
    if (dates.length > 1) {
      analysis.possibleCauses.push("TIMEZONE_CALCULATION_ERROR");
    }

    if (dates.some(d => d !== date)) {
      analysis.possibleCauses.push("DATE_MISMATCH");
    }

    const domains = Object.keys(analysis.domainDistribution);
    if (domains.length > 1 || (domains.length === 1 && domains[0] !== domain)) {
      analysis.possibleCauses.push("DOMAIN_MISMATCH");
    }

    const statuses = Object.keys(analysis.statusDistribution);
    if (statuses.some(s => !['processing', 'shipped', 'completed'].includes(s))) {
      analysis.possibleCauses.push("INVALID_STATUS_INCLUDED");
    }

    discrepancy.analysis.extraOrdersDetails = analysis;

  } catch (error) {
    logger.error("Error analyzing extra orders", {
      error: error.message,
      domain,
      date
    });
  }
}

/**
 * Validate and reconcile sale report data with actual orders
 * @param {string} date - Date in format DD-MM-YYYY
 * @param {string} domain - Optional domain filter
 * @param {boolean} fix - Whether to fix discrepancies automatically
 */
export const validateSaleReportData = async (date, domain = null, fix = false) => {
  try {
    logger.info("Starting sale report validation", { date, domain, fix });

    // Use the same timezone conversion as other parts of the system
    const { startDate, endDate } = convertToClientTimezone(date, date);

    const whereClause = {
      status: {
        [Op.in]: ["processing", "shipped", "completed", "delivered"]  // All paid orders
      },
      createdAt: {
        [Op.between]: [startDate, endDate],
      },
    };

    if (domain) {
      whereClause.domain = domain;
    }

    logger.info("Query parameters", {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      domain: domain || 'all',
      whereClause
    });

    const orders = await Order.findAll({
      where: whereClause,
      order: [['createdAt', 'ASC']],
    });

    logger.info(`Found ${orders.length} paid orders for validation (processing/shipped/completed)`);

    // Group orders by domain and date
    const ordersByDomainDate = {};
    orders.forEach(order => {
      const orderDate = adjustDate(order.createdAt);
      const key = `${order.domain}_${orderDate}`;
      
      if (!ordersByDomainDate[key]) {
        ordersByDomainDate[key] = {
          domain: order.domain,
          date: orderDate,
          orders: [],
          totalMoney: 0,
          totalOrders: 0,
        };
      }
      
      ordersByDomainDate[key].orders.push(order);
      ordersByDomainDate[key].totalMoney = currency(ordersByDomainDate[key].totalMoney)
        .add(order.orderData.total).value;
      ordersByDomainDate[key].totalOrders += 1;
    });

    const discrepancies = [];

    // Validate each domain-date combination
    for (const [, expectedData] of Object.entries(ordersByDomainDate)) {
      const { domain: orderDomain, date: orderDate } = expectedData;

      // Get sale report
      const saleReport = await SaleReport.findOne({
        where: {
          domain: orderDomain,
          date: orderDate,
        },
      });

      // Get domain report
      const domainReport = await DomainReport.findOne({
        where: {
          domain: orderDomain,
          date: orderDate,
        },
      });

      const discrepancy = {
        domain: orderDomain,
        date: orderDate,
        expected: expectedData,
        saleReport: saleReport ? {
          orders: saleReport.orders,
          money: parseFloat(saleReport.money),
          orderIds: saleReport.orderIds,
        } : null,
        domainReport: domainReport ? {
          totalOrders: domainReport.Stats[0]?.total_orders || 0,
          totalSales: domainReport.Stats[0]?.total_sales || 0,
          orderIds: domainReport.orderIds,
        } : null,
        issues: [],
        errorTypes: [],
        analysis: {},
      };

      // Check sale report discrepancies with detailed analysis
      if (!saleReport) {
        discrepancy.issues.push("Sale report missing");
        discrepancy.errorTypes.push("MISSING_SALE_REPORT");
        discrepancy.analysis.saleReportMissing = true;
      } else {
        const expectedOrderIds = expectedData.orders.map(o => o.id);
        const missingOrderIds = expectedOrderIds.filter(id => !saleReport.orderIds.includes(id));
        const extraOrderIds = saleReport.orderIds.filter(id => !expectedOrderIds.includes(id));

        // Analyze missing orders
        if (missingOrderIds.length > 0) {
          discrepancy.issues.push(`Sale report missing order IDs: ${missingOrderIds.join(', ')}`);
          discrepancy.errorTypes.push("MISSING_ORDERS");
          discrepancy.analysis.missingOrders = {
            count: missingOrderIds.length,
            orderIds: missingOrderIds,
            possibleCauses: []
          };

          // Check if missing orders are from different dates
          const missingOrdersDetails = expectedData.orders.filter(o => missingOrderIds.includes(o.id));
          const missingOrderDates = missingOrdersDetails.map(o => adjustDate(o.createdAt));
          const uniqueDates = [...new Set(missingOrderDates)];
          if (uniqueDates.length > 1) {
            discrepancy.analysis.missingOrders.possibleCauses.push("TIMEZONE_ISSUE");
          }

          // Check if missing orders have different statuses
          const missingOrderStatuses = [...new Set(missingOrdersDetails.map(o => o.status))];
          if (missingOrderStatuses.length > 1) {
            discrepancy.analysis.missingOrders.possibleCauses.push("STATUS_FILTER_ISSUE");
          }
        }

        // Analyze extra orders
        if (extraOrderIds.length > 0) {
          discrepancy.issues.push(`Sale report has extra order IDs: ${extraOrderIds.join(', ')}`);
          discrepancy.errorTypes.push("EXTRA_ORDERS");
          discrepancy.analysis.extraOrders = {
            count: extraOrderIds.length,
            orderIds: extraOrderIds,
            possibleCauses: ["DUPLICATE_PROCESSING", "DATE_CALCULATION_ERROR", "TIMEZONE_MISMATCH"]
          };
        }

        // Count and money mismatches
        if (saleReport.orders !== expectedData.totalOrders) {
          discrepancy.issues.push(`Sale report orders mismatch: expected ${expectedData.totalOrders}, got ${saleReport.orders}`);
          discrepancy.errorTypes.push("COUNT_MISMATCH");
          discrepancy.analysis.countMismatch = {
            expected: expectedData.totalOrders,
            actual: saleReport.orders,
            difference: saleReport.orders - expectedData.totalOrders
          };
        }

        if (Math.abs(parseFloat(saleReport.money) - expectedData.totalMoney) > 0.01) {
          discrepancy.issues.push(`Sale report money mismatch: expected ${expectedData.totalMoney}, got ${saleReport.money}`);
          discrepancy.errorTypes.push("MONEY_MISMATCH");
          discrepancy.analysis.moneyMismatch = {
            expected: expectedData.totalMoney,
            actual: parseFloat(saleReport.money),
            difference: parseFloat(saleReport.money) - expectedData.totalMoney
          };
        }
      }

      // Check domain report discrepancies (similar analysis as sale report)
      if (!domainReport) {
        discrepancy.issues.push("Domain report missing");
        discrepancy.errorTypes.push("MISSING_DOMAIN_REPORT");
        discrepancy.analysis.domainReportMissing = true;
      } else {
        const domainStats = domainReport.Stats[0] || {};
        const expectedOrderIds = expectedData.orders.map(o => o.id);
        const missingOrderIds = expectedOrderIds.filter(id => !domainReport.orderIds.includes(id));
        const extraOrderIds = domainReport.orderIds.filter(id => !expectedOrderIds.includes(id));

        if (domainStats.total_orders !== expectedData.totalOrders) {
          discrepancy.issues.push(`Domain report orders mismatch: expected ${expectedData.totalOrders}, got ${domainStats.total_orders}`);
          if (!discrepancy.errorTypes.includes("COUNT_MISMATCH")) {
            discrepancy.errorTypes.push("COUNT_MISMATCH");
          }
        }

        if (Math.abs(domainStats.total_sales - expectedData.totalMoney) > 0.01) {
          discrepancy.issues.push(`Domain report sales mismatch: expected ${expectedData.totalMoney}, got ${domainStats.total_sales}`);
          if (!discrepancy.errorTypes.includes("MONEY_MISMATCH")) {
            discrepancy.errorTypes.push("MONEY_MISMATCH");
          }
        }

        if (missingOrderIds.length > 0) {
          discrepancy.issues.push(`Domain report missing order IDs: ${missingOrderIds.join(', ')}`);
          if (!discrepancy.errorTypes.includes("MISSING_ORDERS")) {
            discrepancy.errorTypes.push("MISSING_ORDERS");
          }
        }

        if (extraOrderIds.length > 0) {
          discrepancy.issues.push(`Domain report has extra order IDs: ${extraOrderIds.join(', ')}`);
          if (!discrepancy.errorTypes.includes("EXTRA_ORDERS")) {
            discrepancy.errorTypes.push("EXTRA_ORDERS");
          }
        }
      }

      if (discrepancy.issues.length > 0) {
        // Add detailed analysis for extra orders
        if (discrepancy.analysis.extraOrders) {
          await analyzeExtraOrders(discrepancy, orderDomain, orderDate);
        }

        discrepancies.push(discrepancy);
        logger.warn("Data discrepancy found", {
          domain: orderDomain,
          date: orderDate,
          errorTypes: discrepancy.errorTypes,
          issues: discrepancy.issues,
          analysis: discrepancy.analysis,
        });
      }
    }

    // Auto-fix disabled to prevent confusion in SaaS environment
    if (fix && discrepancies.length > 0) {
      logger.warn("Auto-fix requested but disabled to prevent data confusion", {
        discrepanciesCount: discrepancies.length,
        reason: "Manual review required for SaaS environment"
      });

      // Return warning instead of fixing
      summary.autoFixDisabled = true;
      summary.autoFixReason = "Auto-fix disabled to prevent confusion in SaaS environment. Manual review required.";
    }

    // Generate comprehensive summary with error analysis
    const errorTypeCounts = {};
    const rootCauseAnalysis = {
      timezoneIssues: 0,
      dateCalculationErrors: 0,
      duplicateProcessing: 0,
      statusFilterIssues: 0,
      missingReports: 0,
      domainMismatches: 0
    };

    discrepancies.forEach(disc => {
      disc.errorTypes.forEach(errorType => {
        errorTypeCounts[errorType] = (errorTypeCounts[errorType] || 0) + 1;
      });

      // Analyze root causes
      if (disc.analysis.extraOrdersDetails) {
        const causes = disc.analysis.extraOrdersDetails.possibleCauses;
        if (causes.includes("TIMEZONE_CALCULATION_ERROR")) rootCauseAnalysis.timezoneIssues++;
        if (causes.includes("DATE_MISMATCH")) rootCauseAnalysis.dateCalculationErrors++;
        if (causes.includes("DOMAIN_MISMATCH")) rootCauseAnalysis.domainMismatches++;
      }

      if (disc.analysis.missingOrders) {
        const causes = disc.analysis.missingOrders.possibleCauses;
        if (causes.includes("TIMEZONE_ISSUE")) rootCauseAnalysis.timezoneIssues++;
        if (causes.includes("STATUS_FILTER_ISSUE")) rootCauseAnalysis.statusFilterIssues++;
      }

      if (disc.analysis.saleReportMissing || disc.analysis.domainReportMissing) {
        rootCauseAnalysis.missingReports++;
      }
    });

    const summary = {
      totalDomainsChecked: Object.keys(ordersByDomainDate).length,
      totalOrdersChecked: orders.length,
      discrepanciesFound: discrepancies.length,
      errorTypeCounts,
      rootCauseAnalysis,
      discrepancies: discrepancies,
      // recommendations: generateRecommendations(rootCauseAnalysis, errorTypeCounts),
    };

    logger.info("Sale report validation completed", summary);
    return summary;

  } catch (error) {
    logger.error("Error validating sale report data", {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const date = process.argv[2];
  const domain = process.argv[3] || null;
  const fix = process.argv[4] === 'true';

  if (!date) {
    console.log("Usage: node validateSaleReportData.js <date> [domain] [fix]");
    console.log("Example: node validateSaleReportData.js 22-06-2025 truestore.us true");
    process.exit(1);
  }

  validateSaleReportData(date, domain, fix)
    .then(result => {
      console.log("Validation completed:", JSON.stringify(result, null, 2));
      process.exit(0);
    })
    .catch(error => {
      console.error("Validation failed:", error);
      process.exit(1);
    });
}
