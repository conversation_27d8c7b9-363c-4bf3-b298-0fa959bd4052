import {
  parse,
  startOfDay,
  endOfDay,
  addMinutes,
  subHours,
  format,
} from "date-fns";
import { nanoid } from "nanoid";
import currency from "currency.js";

// Constants
const clientTimezoneOffset = 7 * 60;

// Date utils
function convertToClientTimezone(startdate, enddate) {
  const parsedStartDate = parse(startdate, "dd-MM-yyyy", new Date());
  const parsedEndDate = parse(enddate, "dd-MM-yyyy", new Date());

  const localTimezoneOffset = new Date().getTimezoneOffset();
  const timezoneDifference = clientTimezoneOffset - localTimezoneOffset;

  const adjustedStartOfDayDate = addMinutes(
    startOfDay(parsedStartDate),
    timezoneDifference,
  );
  const adjustedEndOfDayDate = addMinutes(
    endOfDay(parsedEndDate),
    timezoneDifference,
  );

  return {
    startDate: adjustedStartOfDayDate,
    endDate: adjustedEndOfDayDate,
  };
}

function adjustDate(isoString) {
  const date = new Date(isoString);
  // const adjustedDate = subHours(date, 7);  // Removed: createdAt already timezone-adjusted
  return date.toISOString().split("T")[0];
}

function formatDate(date, formatString) {
  return format(subHours(new Date(date), 7), formatString);
}

function normalizeString(str) {
  return str
    .replace(/[\u{1F000}-\u{1FFFF}]/gu, "") // remove all emoji and symbols
    .replace(/[^\p{L}\p{N}\s-]/gu, "") // remove all non-alphanumeric characters except spaces and hyphens
    .trim() // trim start and end of string
    .replace(/\s+/g, " ") // replace multiple spaces with a single space
    .normalize("NFD") // normalize to decomposed form
    .replace(/[\u0300-\u036f]/g, "") // remove all accents
    .replace(/đ/g, "d")
    .replace(/Đ/g, "D"); // replace Vietnamese characters
}

function getMetaDataValue(metaData, key) {
  if (!metaData || !Array.isArray(metaData)) return "";
  const item = metaData.find((item) => item.key === key);
  return item ? item.value : "";
}

// Order utils
function determineCarrier(trackingNumber) {
  if (!trackingNumber) return "Other";

  const upperTrackingNumber = trackingNumber.toUpperCase();

  if (upperTrackingNumber.startsWith("9")) {
    return "JS EXPRESS";
  }
  if (upperTrackingNumber.startsWith("Y")) {
    return "YUNEXPRESS";
  }
  if (upperTrackingNumber.startsWith("A")) {
    return "CHINA POST";
  }
  if (upperTrackingNumber.startsWith("U")) {
    return "YANWEN";
  }
  return "Other";
}

function generateOrderKey() {
  const timestamp = Date.now().toString();
  const randomId = nanoid(8);
  return `${timestamp}${randomId}`;
}

// Data formatting utils
function formatStats(stats) {
  if (!stats || typeof stats !== "object") {
    return [];
  }
  return Object.entries(stats).map(([key, value]) => ({
    name: key,
    value: value?.toString() || "0",
  }));
}

// Order calculation utils
function calculateOrderTotals(line_items, shipping_lines, free_shipping = false, discount = undefined, tip = undefined) {
  // Calculate subtotal from line items
  const subTotal = line_items.reduce((total, item) => {
    return total + (parseFloat(item.price || 0) * parseInt(item.quantity || 1));
  }, 0);

  // Calculate shipping total if not free shipping
  const shippingTotal = free_shipping ? 0 : shipping_lines.reduce((total, line) => {
    return total + parseFloat(line.total || 0);
  }, 0);

  // Convert discount and tip to numbers, handling undefined values
  const discountAmount = discount !== undefined ? parseFloat(discount) || 0 : 0;
  const tipAmount = tip !== undefined ? parseFloat(tip) || 0 : 0;

  // Calculate final total
  const total = currency(subTotal)
    .add(shippingTotal)
    .subtract(discountAmount)
    .add(tipAmount)
    .value;

  return {
    total: currency(total).value,
    subTotal: currency(subTotal).value,
    shippingTotal: currency(shippingTotal).value,
    discountTotal: currency(discountAmount).value,
    tipTotal: currency(tipAmount).value
  };
}

// Export tất cả các hàm
export {
  // Date utils
  convertToClientTimezone,
  adjustDate,
  formatDate,

  // String utils
  normalizeString,
  getMetaDataValue,

  // Order utils
  determineCarrier,
  generateOrderKey,

  // Data formatting utils
  formatStats,
  
  // Order calculation utils
  calculateOrderTotals,
};
