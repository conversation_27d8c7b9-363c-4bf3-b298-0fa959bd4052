import { validateSaleReportData } from "../scripts/validateSaleReportData.js";
import logger from "../utils/logger.js";

/**
 * API endpoint to validate sale report data consistency
 * GET /api/validate-sale-report?date=22-06-2025&domain=truestore.us&fix=true
 */
export const validateSaleReport = async (req, res) => {
  try {
    const { date, domain, fix } = req.query;

    if (!date) {
      return res.status(400).json({
        success: false,
        error: "Date parameter is required (format: DD-MM-YYYY)",
      });
    }

    // Validate date format
    const dateRegex = /^\d{2}-\d{2}-\d{4}$/;
    if (!dateRegex.test(date)) {
      return res.status(400).json({
        success: false,
        error: "Invalid date format. Use DD-MM-YYYY",
      });
    }

    const shouldFix = fix === 'true';

    logger.info("Starting sale report validation via API", {
      date,
      domain: domain || 'all',
      fix: shouldFix,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
    });

    const result = await validateSaleReportData(date, domain, shouldFix);

    const response = {
      success: true,
      data: {
        validationDate: date,
        domain: domain || 'all',
        autoFixed: shouldFix,
        summary: {
          totalDomainsChecked: result.totalDomainsChecked,
          totalOrdersChecked: result.totalOrdersChecked,
          discrepanciesFound: result.discrepanciesFound,
        },
        discrepancies: result.discrepancies,
      },
      timestamp: new Date().toISOString(),
    };

    if (result.discrepanciesFound > 0) {
      logger.warn("Data discrepancies found during validation", {
        date,
        domain,
        discrepanciesCount: result.discrepanciesFound,
      });
    } else {
      logger.info("No data discrepancies found", { date, domain });
    }

    res.json(response);

  } catch (error) {
    logger.error("Error in sale report validation API", {
      error: error.message,
      stack: error.stack,
      query: req.query,
    });

    res.status(500).json({
      success: false,
      error: "Internal server error during validation",
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * API endpoint to get data consistency health check
 * GET /api/data-health-check?days=7
 */
export const dataHealthCheck = async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 7;
    
    if (days > 30) {
      return res.status(400).json({
        success: false,
        error: "Maximum 30 days allowed for health check",
      });
    }

    const healthResults = [];
    const today = new Date();

    for (let i = 0; i < days; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(today.getDate() - i);
      
      const dateString = checkDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });

      try {
        const result = await validateSaleReportData(dateString, null, false);
        healthResults.push({
          date: dateString,
          status: result.discrepanciesFound === 0 ? 'healthy' : 'issues',
          domainsChecked: result.totalDomainsChecked,
          ordersChecked: result.totalOrdersChecked,
          discrepancies: result.discrepanciesFound,
        });
      } catch (error) {
        healthResults.push({
          date: dateString,
          status: 'error',
          error: error.message,
        });
      }
    }

    const summary = {
      totalDaysChecked: days,
      healthyDays: healthResults.filter(r => r.status === 'healthy').length,
      daysWithIssues: healthResults.filter(r => r.status === 'issues').length,
      daysWithErrors: healthResults.filter(r => r.status === 'error').length,
    };

    res.json({
      success: true,
      data: {
        summary,
        dailyResults: healthResults,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    logger.error("Error in data health check API", {
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      error: "Internal server error during health check",
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  }
};
