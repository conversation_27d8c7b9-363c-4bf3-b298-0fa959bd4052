import currency from "currency.js";
import DomainReport from "../models/domainReport.js";
import { adjustDate } from "../utils/utils.js";

export const updateDomainReport = async (newOrder, options = {}) => {
  const dateString = adjustDate(newOrder.createdAt);
  const orderCurrency = newOrder.orderData.currency || "USD";

  // Support database transactions
  const transactionOptions = options.transaction ? { transaction: options.transaction } : {};

  const [report, created] = await DomainReport.findOrCreate({
    where: {
      domain: newOrder.domain,
      date: dateString,
    },
    defaults: {
      currency: orderCurrency,
      Stats: [
        {
          total_sales: 0,
          total_orders: 0,
          // total_items: 0,
          total_shipping: 0,
          items: [],
        },
      ],
      orderIds: [],
    },
    ...transactionOptions,
  });

  // Check if order is already included to prevent duplicates
  if (!report.orderIds.includes(newOrder.id)) {
    const updatedOrderIds = [...report.orderIds, newOrder.id];
    report.orderIds = updatedOrderIds;

    // Always update to the currency from the current order
    report.currency = orderCurrency;

    const updatedStats = JSON.parse(JSON.stringify(report.Stats));
    updatedStats[0].total_orders += 1;
    updatedStats[0].total_sales = currency(updatedStats[0].total_sales).add(
      newOrder.orderData.total,
    ).value;
    updatedStats[0].total_shipping = currency(updatedStats[0].total_shipping).add(
      newOrder.orderData.shipping_total,
    ).value;

    const productSale = newOrder.orderData.line_items[0];
    const productIndex = updatedStats[0].items.findIndex(
      (p) => p.product_id === productSale.product_id,
    );
    if (productIndex === -1) {
      updatedStats[0].items.push({
        product_id: productSale.product_id,
        quantity: 1,
        revenue: newOrder.orderData.total,
        product_url: productSale.product_link,
        gross: currency(newOrder.orderData.total).subtract(
          newOrder.orderData.shipping_total,
        ).value,
      });
    } else {
      updatedStats[0].items[productIndex].quantity += 1;
      updatedStats[0].items[productIndex].gross = currency(
        updatedStats[0].items[productIndex].gross,
      )
        .add(newOrder.orderData.total)
        .subtract(newOrder.orderData.shipping_total).value;
      updatedStats[0].items[productIndex].revenue = currency(
        updatedStats[0].items[productIndex].revenue,
      ).add(newOrder.orderData.total).value;
    }
    report.set("Stats", updatedStats);
    await report.save(transactionOptions);

    return { updated: true, stats: report.Stats };
  }

  return { updated: false, stats: report.Stats };
};
