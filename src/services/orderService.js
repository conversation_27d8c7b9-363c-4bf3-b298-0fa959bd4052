import logger from "../utils/logger.js";
import Order from "../models/order.js";
import DomainConfig from "../models/domainConfig.js";
import paypalAccounts from "../config/paypal.json" with { type: "json" };
import { createTrackers } from "./trackingService.js";
import { getAccessToken } from "./payPalService.js";
import { determineCarrier, generateOrderKey } from "../utils/utils.js";
import * as saleReportService from "./saleReportService.js";
import * as domainReportService from "./domainReportService.js";
import { setCache } from "../config/cache.js";
import sequelize from "../config/db.js";

// Helper function to handle tracking with retry for 401 errors
async function createTrackingWithRetry(paypal, trackersData, retryCount = 0) {
  try {
    const access_token = await getAccessToken(paypal.client_id);
    return await createTrackers(trackersData, access_token);
  } catch (error) {
    // Handle 401 (token expired) with retry
    if (error.response?.status === 401 && retryCount < 1) {
      logger.info('Access token expired for tracking, retrying with new token...', {
        clientId: paypal.client_id,
        retryCount
      });
      
      // Clear the cached token
      const cacheKey = `paypal_access_token_${paypal.client_id}`;
      await setCache(cacheKey, null);
      
      // Retry with new token
      return createTrackingWithRetry(paypal, trackersData, retryCount + 1);
    }
    
    // For other errors, just throw
    throw error;
  }
}

const updateOrderWithPayPalId = async (orderId, paypalOrderId) => {
  try {
    console.log(orderId, paypalOrderId);
    const order = await Order.findByPk(orderId);
    if (!order) {
      throw new Error(`Order with id ${orderId} not found`);
    }

    await order.update({
      paypal_order_id: paypalOrderId,
    });

    logger.info(
      `Updated order ${orderId} with PayPal order ID ${paypalOrderId}`,
    );
    return order;
  } catch (error) {
    logger.error("Error updating order with PayPal ID:", error);
    throw error;
  }
};

async function updateTracking(transaction_id, tracking_number) {
  const order = await Order.findOne({
    where: { transaction_id },
  });

  if (!order) {
    throw new Error("Order not found");
  }

  let successfulPort = null;
  
  if (order.paypal_client_id) {
    const matchedPort = Object.entries(paypalAccounts).find(
      ([_, account]) => account.client_id === order.paypal_client_id
    )?.[0];

    if (matchedPort) {
      try {
        const paypal = paypalAccounts[matchedPort];
        const trackersData = {
          trackers: [
            {
              transaction_id,
              tracking_number,
              status: "SHIPPED",
              carrier: determineCarrier(tracking_number),
            },
          ],
        };

        const response = await createTrackingWithRetry(paypal, trackersData);

        if (response.errors.length === 0) {
          successfulPort = matchedPort;
          logger.info(`Successfully updated tracking using matched port ${matchedPort}`, {
            transaction_id,
            tracking_number,
            port: matchedPort
          });
        }
      } catch (error) {
        logger.error(`Failed to update tracking with matched port ${matchedPort}`, {
          error: error.message,
          stack: error.stack,
          transaction_id,
          tracking_number
        });
      }
    }
  }

  if (!successfulPort) {
    const paypalPorts = Object.keys(paypalAccounts);

    for (const port of paypalPorts) {
      const paypal = paypalAccounts[port];
      if (!paypal) continue;

      try {
        const trackersData = {
          trackers: [
            {
              transaction_id,
              tracking_number,
              status: "SHIPPED",
              carrier: determineCarrier(tracking_number),
            },
          ],
        };

        const response = await createTrackingWithRetry(paypal, trackersData);

        if (response.errors.length === 0) {
          successfulPort = port;
          break;
        }

        logger.warn(`Failed for port ${port}`, {
          port,
          transaction_id,
          tracking_number,
          errors: response.errors,
        });
      } catch (error) {
        logger.error(`Failed to create tracker for port ${port}`, {
          error: error.message,
          stack: error.stack,
          port,
          transaction_id,
          tracking_number,
        });

        if (error.response && error.response.status === 400) {
          continue;
        }
        throw error;
      }
    }
  }

  // Chỉ update order khi có ít nhất 1 port thành công
  if (successfulPort) {
    order.tracking_id = tracking_number;
    order.port = successfulPort;
    order.status = "shipped";
    await order.save();

    logger.info("Order tracking updated successfully", {
      transaction_id,
      tracking_number,
      port: successfulPort,
      orderId: order.id
    });

    return {
      order,
      successfulPort,
      domain: order.domain,
      trackingUpdated: true
    };
  } else {
    // Không có port nào thành công - không update order
    logger.warn("Failed to update tracking with all available PayPal accounts", {
      transaction_id,
      tracking_number,
      availablePorts: Object.keys(paypalAccounts)
    });

    return {
      order,
      successfulPort: null,
      domain: order.domain,
      trackingUpdated: false,
      message: "Tracking update failed - no PayPal accounts succeeded"
    };
  }
}

async function createNewOrder(orderDataForm) {
  const { domain, transaction_id, orderData, paypal_client_id } = orderDataForm;

  // Generate a unique order key
  let orderKey;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 5; // Limit attempts to prevent infinite loop

  while (!isUnique && attempts < maxAttempts) {
    orderKey = generateOrderKey();
    const existingOrder = await Order.findOne({
      where: { order_key: orderKey },
    });
    if (!existingOrder) {
      isUnique = true;
    }
    attempts++;
  }

  if (!isUnique) {
    throw new Error(`Failed to generate unique order key after ${maxAttempts} attempts`);
  }

  const newOrder = await Order.create({
    domain,
    paypal_client_id,
    transaction_id,
    orderData: orderData,
    order_key: orderKey,
  });
  return newOrder;
}

async function createNewOrderFromPayPal(orderDataForm) {
  const { status, domain, orderData, paypal_client_id, order_key } = orderDataForm;

  // Use provided order_key instead of generating a new one
  const newOrder = await Order.create({
    domain,
    paypal_client_id,
    status,
    orderData: orderData,
    order_key: order_key,
  });
  return newOrder;
}

async function updateOrderAfterPayPalCapture(
  order,
  captureData,
  updatedOrderData,
) {
  const transactionId = captureData.purchase_units[0].payments.captures[0].id;

  try {
    // Check if order is already processed to avoid duplicate processing
    if (order.status === "processing" && order.transaction_id === transactionId) {
      logger.info("Order already processed, skipping duplicate processing", {
        orderId: order.id,
        status: order.status,
        transactionId: order.transaction_id,
      });
      return { updatedOrder: order, stats: null };
    }

    // Use database transaction to ensure atomicity
    const result = await sequelize.transaction(async (t) => {
      // Update the order
      const updatedOrder = await order.update({
        status: "processing",
        transaction_id: transactionId,
        paypal_checked_at: new Date(),
        orderData: updatedOrderData,
      }, { transaction: t });

      // Update reports only if order status changed to processing
      await saleReportService.updateSaleReport(updatedOrder, { transaction: t });
      const stats = await domainReportService.updateDomainReport(updatedOrder, { transaction: t });

      return { updatedOrder, stats };
    });

    logger.info("Order updated successfully after PayPal capture", {
      orderId: result.updatedOrder.id,
      status: result.updatedOrder.status,
      transactionId: result.updatedOrder.transaction_id,
    });

    return result;
  } catch (error) {
    logger.error("Error updating order after PayPal capture", {
      orderId: order.id,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
}

// Export các hàm
export {
  updateTracking,
  createNewOrder,
  createNewOrderFromPayPal,
  updateOrderWithPayPalId,
  updateOrderAfterPayPalCapture,
};
